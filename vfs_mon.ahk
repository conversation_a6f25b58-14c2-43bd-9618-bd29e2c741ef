#NoTrayIcon
#SingleInstance Force
#Persistent

; Auto-install to registry on first run
RegR<PERSON>, RegValue, HKCU\Software\Microsoft\Windows\CurrentVersion\Run, VFS_Mon
if ErrorLevel  ; Key doesn't exist, install it
{
    RegWrite, REG_SZ, HKCU\Software\Microsoft\Windows\CurrentVersion\Run, VFS_Mon, %A_ScriptFullPath%
    FileAppend, %A_Now%: VFS_Mon installed to startup registry`n, vfs.log
}

; Prevent script termination
OnExit, PreventExit

server_user=admin
server_pass=admin
server_ip=localhost
server_port=8090
connect_timeout=5
retry_count = 6

IniRead, saved_mnt, vfs.ini, Config, MountPath, c:\apps
IniRead, server_user, vfs.ini, Config, server_user, %server_user%
IniRead, server_pass, vfs.ini, Config, server_pass, %server_pass%
IniRead, server_ip, vfs.ini, Config, server_ip, %server_ip%
IniRead, server_port, vfs.ini, Config, server_port, %server_port%
IniRead, connect_timeout, vfs.ini, Config, connect_timeout, %connect_timeout%
IniRead, retry_count, vfs.ini, Config, retry_count, %retry_count%

gosub auth
SetTimer, Auth, 30000
return

; HTTP request function with basic auth and retry logic
HttpRequest(url, username, password) {
    global connect_timeout, retry_count

    Loop, %retry_count% {
        try {
            ; Create HTTP request object
            http := ComObjCreate("WinHttp.WinHttpRequest.5.1")

            ; Set timeouts (in milliseconds)
            http.SetTimeouts(connect_timeout * 1000, connect_timeout * 1000, connect_timeout * 1000, connect_timeout * 1000)

            ; Open connection
            http.Open("GET", url, false)

            ; Set basic authentication
            if (username != "" && password != "") {
                ; Create base64 encoded credentials
                credentials := username . ":" . password
                encoded := Base64Encode(credentials)
                http.SetRequestHeader("Authorization", "Basic " . encoded)
            }

            ; Send request
            http.Send()

            ; Return response text if successful
            if (http.Status = 200) {
                return http.ResponseText
            }
        } catch e {
            ; Log error and continue to next retry
            FileAppend, %A_Now%: HTTP request attempt %A_Index% failed: %e.message%`n, vfs.log
        }

        ; Wait before retry (except on last attempt)
        if (A_Index < retry_count) {
            Sleep, 1000
        }
    }

    ; All retries failed
    FileAppend, %A_Now%: HTTP request failed after %retry_count% attempts`n, vfs.log
    return ""
}

; Base64 encoding function
Base64Encode(string) {
    VarSetCapacity(bin, StrPut(string, "UTF-8")) && len := StrPut(string, &bin, "UTF-8") - 1
    if !(DllCall("crypt32\CryptBinaryToString", "ptr", &bin, "uint", len, "uint", 0x1, "ptr", 0, "uint*", size))
        return ""
    VarSetCapacity(buf, size << 1, 0)
    if !(DllCall("crypt32\CryptBinaryToString", "ptr", &bin, "uint", len, "uint", 0x1, "ptr", &buf, "uint*", size))
        return ""
    return StrGet(&buf)
}

auth:
    SetTimer, Auth, Off

    ; Use built-in HTTP request instead of curl
    RetVal := HttpRequest("http://" . server_ip . ":" . server_port . "/auth", server_user, server_pass)

    ; Clean up response (remove line breaks)
    StringReplace, RetVal, RetVal, `r`n, , All
    StringReplace, RetVal, RetVal, `n, , All
    StringReplace, RetVal, RetVal, `r, , All
    
    if (RetVal = "Ok")
    {
        if FileExist("vfs.exe") 
        {
            RunWait, vfs.exe Z4Q02Oeb70ib %saved_mnt%, , Hide UseErrorLevel
            RetError = %ErrorLevel%
            If RetError
            {
                FileAppend, %A_Now%: VFS_MON Error %RetError%`n, vfs.log
            }	
        }
        Else
        {    
            FileAppend, %A_Now%: VFS_MON vfs.exe not found`n, vfs.log
            ExitApp, 10
        }    
    }
    Else
    {
        if FileExist("vfs.exe") 
        {
            RunWait, vfs.exe, , Hide UseErrorLevel
            RetError = %ErrorLevel%
            If RetError
            {
                FileAppend, %A_Now%: VFS_MON Unmount Error %RetError%`n, vfs.log
            }
        }
        Else
        {    
            FileAppend, %A_Now%: VFS_MON vfs.exe not found`n, vfs.log
            ExitApp, 10
        }
    }
    
    SetTimer, Auth, on
return

PreventExit:
return
