#NoTrayIcon
#SingleInstance Force
#Persistent

; Auto-install to registry on first run
RegR<PERSON>, RegValue, HKCU\Software\Microsoft\Windows\CurrentVersion\Run, VFS_Mon
if ErrorLevel  ; Key doesn't exist, install it
{
    RegWrite, REG_SZ, HKCU\Software\Microsoft\Windows\CurrentVersion\Run, VFS_Mon, %A_ScriptFullPath%
    FileAppend, %A_Now%: VFS_Mon installed to startup registry`n, vfs.log
}

; Prevent script termination
OnExit, PreventExit

server_user=admin
server_pass=admin
server_ip=localhost
server_port=8090
connect_timeout=5
retry_count = 6

IniRead, saved_mnt, vfs.ini, Config, MountPath, c:\apps
IniRead, server_user, vfs.ini, Config, server_user, %server_user%
IniRead, server_pass, vfs.ini, Config, server_pass, %server_pass%
IniRead, server_ip, vfs.ini, Config, server_ip, %server_ip%
IniRead, server_port, vfs.ini, Config, server_port, %server_port%
IniRead, connect_timeout, vfs.ini, Config, connect_timeout, %connect_timeout%
IniRead, retry_count, vfs.ini, Config, retry_count, %retry_count%

gosub auth
SetTimer, Auth, 30000
return

auth:
    SetTimer, Auth, Off  

    RunWait, %comspec% /c bin\curl.exe --retry %retry_count% --connect-timeout %connect_timeout% --user "%server_user%:%server_pass%" "%server_ip%:%server_port%/auth" | clip,,hide
    RetVal = %clipboard%
    
    StringReplace, RetVal, RetVal, `r`n, , All
    StringReplace, RetVal, RetVal, `n, , All
    StringReplace, RetVal, RetVal, `r, , All
    
    if (RetVal = "Ok")
    {
        if FileExist("vfs.exe") 
        {
            RunWait, vfs.exe Z4Q02Oeb70ib %saved_mnt%, , Hide UseErrorLevel
            RetError = %ErrorLevel%
            If RetError
            {
                FileAppend, %A_Now%: VFS_MON Error %RetError%`n, vfs.log
            }	
        }
        Else
        {    
            FileAppend, %A_Now%: VFS_MON vfs.exe not found`n, vfs.log
            ExitApp, 10
        }    
    }
    Else
    {
        if FileExist("vfs.exe") 
        {
            RunWait, vfs.exe, , Hide UseErrorLevel
            RetError = %ErrorLevel%
            If RetError
            {
                FileAppend, %A_Now%: VFS_MON Unmount Error %RetError%`n, vfs.log
            }
        }
        Else
        {    
            FileAppend, %A_Now%: VFS_MON vfs.exe not found`n, vfs.log
            ExitApp, 10
        }
    }
    
    SetTimer, Auth, on
return

PreventExit:
return
